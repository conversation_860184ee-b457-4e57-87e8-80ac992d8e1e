#!/usr/bin/env python3
"""
Comprehensive test suite for the Research Engine
Tests the actual ResearchEngine class and its methods
"""

import pytest
import asyncio
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock

# Add src to path for imports
import sys
import os
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from research_engine import (
    ResearchEngine,
    ResearchQuery,
    ResearchContext,
    CrawlRequest,
    ResearchResult,
    ResearchType,
    ResearchPriority,
    ResearchEngineError,
    CrawlError,
    SearchError,
    ResearchEngineExecutor
)
from research_config import ResearchConfig, ResearchMode


class TestResearchEngine:
    """Test the ResearchEngine class"""
    
    def setup_method(self):
        """Setup test environment"""
        self.mcp_url = "http://localhost:8051"
        self.openai_api_key = "test-key"
        
    def test_research_engine_initialization(self):
        """Test ResearchEngine initialization"""
        engine = ResearchEngine(self.mcp_url, self.openai_api_key)
        
        assert engine is not None
        assert engine.mcp_url == self.mcp_url
        assert engine.openai_api_key == self.openai_api_key
        assert hasattr(engine, 'research_templates')
        assert hasattr(engine, 'source_priorities')
        assert hasattr(engine, 'query_patterns')
    
    def test_research_query_creation(self):
        """Test ResearchQuery creation"""
        query = ResearchQuery(
            query="React best practices",
            research_type=ResearchType.BEST_PRACTICES,
            priority=ResearchPriority.HIGH,
            max_results=10,
            include_code=True
        )
        
        assert query.query == "React best practices"
        assert query.research_type == ResearchType.BEST_PRACTICES
        assert query.priority == ResearchPriority.HIGH
        assert query.max_results == 10
        assert query.include_code is True
    
    def test_research_context_creation(self):
        """Test ResearchContext creation"""
        context = ResearchContext(
            project_type="web_application",
            technology_stack=["React", "TypeScript"],
            requirements=["scalable", "secure"],
            complexity_level="intermediate"
        )
        
        assert context.project_type == "web_application"
        assert "React" in context.technology_stack
        assert "scalable" in context.requirements
        assert context.complexity_level == "intermediate"
    
    def test_crawl_request_creation(self):
        """Test CrawlRequest creation"""
        request = CrawlRequest(
            url="https://reactjs.org/docs",
            crawl_type="smart",
            max_pages=10,
            include_code=True
        )
        
        assert request.url == "https://reactjs.org/docs"
        assert request.crawl_type == "smart"
        assert request.max_pages == 10
        assert request.include_code is True
    
    def test_research_result_creation(self):
        """Test ResearchResult creation"""
        result = ResearchResult(
            query="React hooks",
            content="React hooks are functions...",
            source_url="https://reactjs.org/docs/hooks",
            source_domain="reactjs.org",
            relevance_score=0.95,
            research_type=ResearchType.DOCUMENTATION,
            code_examples=["const [state, setState] = useState()"]
        )
        
        assert result.query == "React hooks"
        assert result.source_domain == "reactjs.org"
        assert result.relevance_score == 0.95
        assert result.research_type == ResearchType.DOCUMENTATION
        assert len(result.code_examples) == 1
    
    def test_source_priority_initialization(self):
        """Test source priority initialization"""
        engine = ResearchEngine()
        
        priorities = engine.source_priorities
        assert priorities["docs.python.org"] == 10
        assert priorities["reactjs.org"] == 9
        assert priorities["stackoverflow.com"] == 8
        assert priorities["default"] == 6
    
    def test_research_template_initialization(self):
        """Test research template initialization"""
        engine = ResearchEngine()
        
        templates = engine.research_templates
        assert "web_application" in templates
        assert "mobile_application" in templates
        assert "api_service" in templates
        assert len(templates["web_application"]) > 0
    
    def test_query_pattern_initialization(self):
        """Test query pattern initialization"""
        engine = ResearchEngine()
        
        patterns = engine.query_patterns
        assert ResearchType.DOCUMENTATION.value in patterns
        assert ResearchType.CODE_EXAMPLES.value in patterns
        assert ResearchType.BEST_PRACTICES.value in patterns
    
    def test_expand_query(self):
        """Test query expansion"""
        engine = ResearchEngine()
        
        query = ResearchQuery(
            query="React best practices",
            research_type=ResearchType.BEST_PRACTICES
        )
        
        context = ResearchContext(
            project_type="web_application",
            technology_stack=["React", "TypeScript"]
        )
        
        expanded = engine._expand_query(query, context)
        
        assert len(expanded) > 1
        assert "React best practices" in expanded
        assert any("React" in q for q in expanded)
    
    def test_extract_domain(self):
        """Test domain extraction from URL"""
        engine = ResearchEngine()
        
        domain = engine._extract_domain("https://reactjs.org/docs/getting-started")
        assert domain == "reactjs.org"
        
        domain = engine._extract_domain("https://docs.python.org/3/")
        assert domain == "docs.python.org"
        
        domain = engine._extract_domain("invalid-url")
        assert domain == ""
    
    def test_get_priority_value(self):
        """Test priority value conversion"""
        engine = ResearchEngine()
        
        assert engine._get_priority_value(ResearchPriority.LOW) == 1
        assert engine._get_priority_value(ResearchPriority.MEDIUM) == 2
        assert engine._get_priority_value(ResearchPriority.HIGH) == 3
        assert engine._get_priority_value(ResearchPriority.CRITICAL) == 4
    
    def test_generate_cache_key(self):
        """Test cache key generation"""
        engine = ResearchEngine()
        
        key1 = engine._generate_cache_key("React hooks", ResearchType.DOCUMENTATION)
        key2 = engine._generate_cache_key("React hooks", ResearchType.DOCUMENTATION)
        key3 = engine._generate_cache_key("Vue hooks", ResearchType.DOCUMENTATION)
        
        assert key1 == key2  # Same query and type should generate same key
        assert key1 != key3  # Different query should generate different key
        assert len(key1) == 32  # MD5 hash length
    
    def test_deduplicate_results(self):
        """Test result deduplication"""
        engine = ResearchEngine()
        
        results = [
            ResearchResult(
                query="test",
                content="content1",
                source_url="https://example.com/1",
                source_domain="example.com",
                relevance_score=0.9,
                research_type=ResearchType.DOCUMENTATION
            ),
            ResearchResult(
                query="test",
                content="content2",
                source_url="https://example.com/1",  # Duplicate URL
                source_domain="example.com",
                relevance_score=0.8,
                research_type=ResearchType.DOCUMENTATION
            ),
            ResearchResult(
                query="test",
                content="content3",
                source_url="https://example.com/2",
                source_domain="example.com",
                relevance_score=0.7,
                research_type=ResearchType.DOCUMENTATION
            )
        ]
        
        unique_results = engine._deduplicate_results(results)
        
        assert len(unique_results) == 2  # Should remove one duplicate
        urls = [r.source_url for r in unique_results]
        assert len(set(urls)) == len(urls)  # All URLs should be unique
    
    def test_rank_results(self):
        """Test result ranking"""
        engine = ResearchEngine()
        
        query = ResearchQuery(
            query="React hooks",
            research_type=ResearchType.DOCUMENTATION,
            include_code=True
        )
        
        context = ResearchContext(project_type="web_application")
        
        results = [
            ResearchResult(
                query="React hooks",
                content="content1",
                source_url="https://reactjs.org/docs",
                source_domain="reactjs.org",
                relevance_score=0.8,
                research_type=ResearchType.DOCUMENTATION
            ),
            ResearchResult(
                query="React hooks",
                content="content2",
                source_url="https://example.com/blog",
                source_domain="example.com",
                relevance_score=0.9,
                research_type=ResearchType.DOCUMENTATION
            )
        ]
        
        ranked_results = engine._rank_results(results, query, context)
        
        # Should be sorted by relevance score (after boosting)
        assert len(ranked_results) == 2
        # reactjs.org should be boosted higher than example.com
        assert ranked_results[0].source_domain == "reactjs.org"


class TestResearchEngineExecutor:
    """Test the Research Engine Executor"""
    
    def test_executor_initialization(self):
        """Test ResearchEngineExecutor initialization"""
        executor = ResearchEngineExecutor()
        
        assert executor is not None
        assert executor.research_engine is None  # Not initialized until execution
    
    def test_execute_research_data_conversion(self):
        """Test research data conversion in executor"""
        research_queries = [
            {
                "query": "React best practices",
                "research_type": "best_practices",
                "priority": "high",
                "max_results": 5,
                "include_code": True
            }
        ]

        context_data = {
            "project_type": "web_application",
            "technology_stack": ["React", "TypeScript"],
            "requirements": ["scalable"]
        }

        # Test data conversion without actual execution
        assert len(research_queries) == 1
        assert research_queries[0]["query"] == "React best practices"
        assert context_data["project_type"] == "web_application"
        assert "React" in context_data["technology_stack"]


class TestResearchConfig:
    """Test the Research Configuration"""
    
    def test_research_config_initialization(self):
        """Test ResearchConfig initialization"""
        config = ResearchConfig()
        
        assert config.mcp_url == "http://localhost:8051"
        assert config.mcp_timeout == 120
        assert config.enable_result_caching is True
        assert config.max_results_per_query == 20
    
    def test_research_mode_config(self):
        """Test research mode configuration"""
        fast_config = ResearchConfig.get_research_mode_config(ResearchMode.FAST)
        comprehensive_config = ResearchConfig.get_research_mode_config(ResearchMode.COMPREHENSIVE)
        
        assert fast_config["max_queries"] < comprehensive_config["max_queries"]
        assert fast_config["timeout_seconds"] < comprehensive_config["timeout_seconds"]
        assert fast_config["enable_code_search"] is False
        assert comprehensive_config["enable_code_search"] is True
    
    def test_source_priorities(self):
        """Test source priority configuration"""
        priorities = ResearchConfig.get_source_priorities()
        
        assert priorities["docs.python.org"] == 10
        assert priorities["reactjs.org"] == 9
        assert priorities["stackoverflow.com"] == 7
        assert priorities["default"] == 5
    
    def test_technology_keywords(self):
        """Test technology keyword configuration"""
        keywords = ResearchConfig.get_technology_keywords()
        
        assert "javascript" in keywords
        assert "python" in keywords
        assert "react" in keywords["javascript"]
        assert "django" in keywords["python"]


class TestResearchTypes:
    """Test research type enums and data structures"""
    
    def test_research_type_enum(self):
        """Test ResearchType enum values"""
        assert ResearchType.DOCUMENTATION.value == "documentation"
        assert ResearchType.CODE_EXAMPLES.value == "code_examples"
        assert ResearchType.BEST_PRACTICES.value == "best_practices"
        assert ResearchType.TUTORIALS.value == "tutorials"
        assert ResearchType.SECURITY.value == "security"
    
    def test_research_priority_enum(self):
        """Test ResearchPriority enum values"""
        assert ResearchPriority.LOW.value == "low"
        assert ResearchPriority.MEDIUM.value == "medium"
        assert ResearchPriority.HIGH.value == "high"
        assert ResearchPriority.CRITICAL.value == "critical"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
